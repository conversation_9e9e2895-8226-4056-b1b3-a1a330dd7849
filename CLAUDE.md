# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

The Ultimate Electrical Designer is a full-stack electrical engineering application built with:
- **Backend**: Python FastAPI with SQLAlchemy, PostgreSQL/SQLite
- **Frontend**: Next.js 15+ with TypeScript, React Query, Zustand
- **Architecture**: 5-layer backend architecture with component-based frontend
- **Quality Standards**: Zero-tolerance policies for errors, comprehensive testing, type safety

## Common Development Commands

### Backend Development (from `server/` directory)

```bash
# Start development server
poetry run python main.py run --reload

# Run tests
poetry run pytest                     # All tests
poetry run pytest -v -m unit         # Unit tests only
poetry run pytest -v -m integration  # Integration tests only
poetry run pytest --cov=src --cov-report=html  # With coverage

# Type checking and linting
poetry run mypy src/                  # Type checking
poetry run ruff check .              # Linting
poetry run ruff format .             # Code formatting

# Database operations
poetry run python main.py migrate                    # Run migrations
poetry run python main.py create-superuser 'Admin' 'Pass123' '<EMAIL>'
poetry run python main.py wipe-database --confirm   # Reset database (dev only)
```

### Frontend Development (from `client/` directory)

```bash
# Start development server
npm run dev

# Build and test
npm run build                         # Production build
npm run type-check                    # TypeScript checking
npm run lint                          # ESLint
npm run test                          # Unit tests with Vitest
npm run test:coverage                 # Unit tests with coverage
npm run test:e2e                      # End-to-end tests with Playwright
```

### Global Development Commands (from root directory)

```bash
# Using Makefile
make help                             # Show all available commands
make install                          # Install all dependencies
make type-check                       # Comprehensive type checking
make test                            # Run all tests
make lint                            # Run linting
make clean                           # Clean temporary files
make dev                             # Start backend development server
```

## Project Architecture

### Backend (5-Layer Architecture)

```
server/src/
├── api/                 # API Layer - FastAPI routes and endpoints
│   ├── v1/             # API version 1 routes
│   └── main_router.py  # Main API router
├── core/               # Business Logic Layer
│   ├── models/         # SQLAlchemy ORM models
│   ├── schemas/        # Pydantic validation schemas
│   ├── services/       # Business logic services
│   ├── repositories/   # Data access layer
│   ├── enums/          # Domain-specific enumerations
│   ├── errors/         # Unified error handling
│   ├── security/       # Security validation
│   └── utils/          # Utility functions
├── config/             # Configuration Layer
├── middleware/         # Middleware components
└── main.py            # Application entry point
```

### Frontend (Component-Based Architecture)

```
client/src/
├── app/                # Next.js App Router pages
├── components/         # Reusable UI components
│   ├── auth/          # Authentication components
│   ├── admin/         # Admin dashboard components
│   ├── ui/            # Base UI components
│   └── common/        # Common components
├── hooks/             # Custom React hooks
│   ├── api/           # API-specific hooks
│   └── useAuth.ts     # Main authentication hook
├── lib/               # Core utilities and configuration
│   ├── api/           # API client
│   ├── auth/          # Authentication utilities
│   └── react-query.tsx # React Query configuration
├── stores/            # Zustand state management
├── types/             # TypeScript type definitions
└── utils/             # Utility functions
```

## Key Development Patterns

### Backend Patterns

1. **Unified Error Handling**: Use `@handle_database_errors` decorator for consistent error handling
2. **Repository Pattern**: All data access goes through repository classes
3. **Service Layer**: Business logic is contained in service classes
4. **Dependency Injection**: Use FastAPI's dependency injection system
5. **Type Safety**: All code must pass MyPy validation

### Frontend Patterns

1. **API Integration**: Use React Query hooks for server state management
2. **Authentication**: Use `useAuth` hook for authentication state
3. **State Management**: Zustand for client state, React Query for server state
4. **Component Structure**: Follow atomic design methodology
5. **Type Safety**: Strict TypeScript with comprehensive type definitions

## Testing Strategy

### Backend Testing

- **Unit Tests**: Test individual functions and classes
- **Integration Tests**: Test API endpoints and database interactions
- **Performance Tests**: Test calculation performance and memory usage
- **Markers**: Use pytest markers (unit, integration, api, database, etc.)

### Frontend Testing

- **Unit Tests**: Vitest + React Testing Library for component testing
- **Integration Tests**: Test component interactions and API integration
- **E2E Tests**: Playwright for full user workflow testing
- **Coverage**: Aim for >85% test coverage

## Quality Standards

### Zero-Tolerance Policies

1. **No Warnings**: All linting and type checking must pass without warnings
2. **No Technical Debt**: Address technical debt immediately
3. **Type Safety**: 100% type annotations (Python) and strict TypeScript
4. **Security**: No security vulnerabilities allowed
5. **Performance**: Optimize for Core Web Vitals and server performance

### Code Quality Tools

- **Backend**: Ruff (linting), MyPy (type checking), Pytest (testing)
- **Frontend**: ESLint (linting), TypeScript (type checking), Vitest/Playwright (testing)
- **Security**: Bandit (Python security), automated security scanning

## Authentication & Security

### Authentication Flow

1. User logs in via `/api/v1/auth/login`
2. JWT token stored in localStorage (via TokenManager)
3. Token attached to API requests via ApiClient
4. useAuth hook manages authentication state
5. Route guards protect authenticated routes

### Security Features

- JWT token authentication
- Password hashing with bcrypt
- Role-based access control (RBAC)
- Input validation and sanitization
- CORS configuration
- Rate limiting middleware

## Database Management

### Development Database

- SQLite for development (in `server/data/`)
- PostgreSQL for production
- Alembic for migrations

### Common Database Operations

```bash
# Create new migration
cd server/src && poetry run alembic revision --autogenerate -m "description"

# Apply migrations
poetry run python main.py migrate

# Reset database (development only)
poetry run python main.py wipe-database --confirm

# Create admin user
poetry run python main.py create-superuser 'Admin' 'Pass123' '<EMAIL>'
```

## Development Workflow

### 5-Phase Methodology

1. **Discovery & Analysis**: Understand requirements and current state
2. **Task Planning**: Break down into manageable tasks
3. **Implementation**: Code with engineering-grade quality
4. **Verification**: Comprehensive testing and validation
5. **Documentation**: Update documentation and create handover package

### Unified Patterns

- Use consistent error handling patterns
- Apply performance monitoring decorators
- Follow established service and repository patterns
- Implement proper logging and monitoring

## Important Files and Locations

### Configuration Files

- `server/pyproject.toml`: Python dependencies and tool configuration
- `client/package.json`: Node.js dependencies and scripts
- `server/src/config/settings.py`: Application configuration
- `Makefile`: Global development commands

### Key Components

- `server/src/app.py`: FastAPI application setup
- `server/src/main.py`: CLI commands and application entry point
- `client/src/hooks/useAuth.ts`: Authentication hook
- `client/src/lib/api/client.ts`: API client
- `server/src/core/models/base.py`: Base model classes

### Documentation

- `docs/developer-handbooks/`: Comprehensive development guides
- `docs/001-development-roadmap.md`: Project phases and milestones
- `.augment/rules/rules.md`: AI agent development rules

## Professional Standards

This project adheres to professional electrical engineering standards:
- IEEE electrical engineering standards
- IEC international standards (IEC-60079, IEC-61508, IEC-60364)
- EN European standards (EN-50110, EN-60204, EN-50522)

All engineering calculations and design elements must comply with these standards.

## Notes for Claude Code

1. **Always run tests** after making changes: `make test` or individual test commands
2. **Check type safety** before committing: `make type-check`
3. **Follow architectural patterns** described above
4. **Use existing utilities** and patterns rather than creating new ones
5. **Maintain zero-tolerance quality standards**
6. **Document all public APIs** and component interfaces
7. **Consider both backend and frontend** when making changes that affect the API
8. **Use the 5-phase methodology** for complex tasks
9. **Apply unified patterns** for consistency across the codebase
10. **Prioritize security and performance** in all implementations