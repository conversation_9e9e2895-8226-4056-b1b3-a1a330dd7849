#!/usr/bin/env python3
"""Advanced Cache Manager for Component Management System.

This module provides sophisticated caching capabilities including:
- Multi-level caching (Redis + in-memory)
- Smart cache invalidation strategies
- Query result caching with intelligent keys
- Specification-based cache warming
- Performance monitoring and metrics
"""

import hashlib
import json
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, Union

import redis
from redis.exceptions import RedisError

from src.config.logging_config import logger

# In-memory cache for frequently accessed data
_memory_cache: Dict[str, Any] = {}
_memory_cache_timestamps: Dict[str, datetime] = {}


@dataclass
class CacheConfig:
    """Configuration for cache behavior."""
    
    # Redis configuration
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None
    
    # Cache TTL settings (in seconds)
    default_ttl: int = 3600  # 1 hour
    search_results_ttl: int = 1800  # 30 minutes
    component_details_ttl: int = 7200  # 2 hours
    specification_values_ttl: int = 86400  # 24 hours
    
    # Memory cache settings
    memory_cache_max_size: int = 1000
    memory_cache_ttl: int = 300  # 5 minutes
    
    # Cache key prefixes
    component_prefix: str = "comp"
    search_prefix: str = "search"
    spec_prefix: str = "spec"
    stats_prefix: str = "stats"


class AdvancedCacheManager:
    """Advanced cache manager with multi-level caching and smart invalidation."""
    
    def __init__(self, config: Optional[CacheConfig] = None):
        """Initialize the cache manager.
        
        Args:
            config: Cache configuration (uses defaults if None)
        """
        self.config = config or CacheConfig()
        self._redis_client: Optional[redis.Redis] = None
        self._initialize_redis()
        
    def _initialize_redis(self) -> None:
        """Initialize Redis connection."""
        try:
            self._redis_client = redis.Redis(
                host=self.config.redis_host,
                port=self.config.redis_port,
                db=self.config.redis_db,
                password=self.config.redis_password,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5,
                retry_on_timeout=True
            )
            
            # Test connection
            self._redis_client.ping()
            logger.info("Redis cache connection established")
            
        except (RedisError, ConnectionError) as e:
            logger.warning(f"Redis connection failed: {e}. Using memory cache only.")
            self._redis_client = None
    
    def _generate_cache_key(self, prefix: str, *args: Any, **kwargs: Any) -> str:
        """Generate a consistent cache key from arguments.
        
        Args:
            prefix: Cache key prefix
            *args: Positional arguments
            **kwargs: Keyword arguments
            
        Returns:
            str: Generated cache key
        """
        # Create a string representation of all arguments
        key_parts = [prefix]
        
        # Add positional arguments
        for arg in args:
            if isinstance(arg, (dict, list)):
                key_parts.append(json.dumps(arg, sort_keys=True))
            else:
                key_parts.append(str(arg))
        
        # Add keyword arguments
        if kwargs:
            sorted_kwargs = sorted(kwargs.items())
            key_parts.append(json.dumps(sorted_kwargs, sort_keys=True))
        
        # Create hash for long keys
        key_string = ":".join(key_parts)
        if len(key_string) > 200:
            key_hash = hashlib.md5(key_string.encode()).hexdigest()
            return f"{prefix}:hash:{key_hash}"
        
        return key_string
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get value from cache (checks memory first, then Redis).
        
        Args:
            key: Cache key
            default: Default value if key not found
            
        Returns:
            Cached value or default
        """
        # Check memory cache first
        if key in _memory_cache:
            timestamp = _memory_cache_timestamps.get(key)
            if timestamp and datetime.now() - timestamp < timedelta(seconds=self.config.memory_cache_ttl):
                logger.debug(f"Cache hit (memory): {key}")
                return _memory_cache[key]
            else:
                # Expired, remove from memory cache
                _memory_cache.pop(key, None)
                _memory_cache_timestamps.pop(key, None)
        
        # Check Redis cache
        if self._redis_client:
            try:
                value = self._redis_client.get(key)
                if value is not None:
                    logger.debug(f"Cache hit (Redis): {key}")
                    # Deserialize JSON
                    try:
                        deserialized_value = json.loads(str(value))
                        # Store in memory cache for faster access
                        self._store_in_memory_cache(key, deserialized_value)
                        return deserialized_value
                    except json.JSONDecodeError:
                        return value
            except RedisError as e:
                logger.warning(f"Redis get error for key {key}: {e}")
        
        logger.debug(f"Cache miss: {key}")
        return default
    
    def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        store_in_memory: bool = True
    ) -> bool:
        """Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (uses default if None)
            store_in_memory: Whether to also store in memory cache
            
        Returns:
            bool: True if successful
        """
        ttl = ttl or self.config.default_ttl
        
        # Store in memory cache
        if store_in_memory:
            self._store_in_memory_cache(key, value)
        
        # Store in Redis
        if self._redis_client:
            try:
                # Serialize to JSON
                serialized_value = json.dumps(value, default=str)
                result = self._redis_client.setex(key, ttl, serialized_value)
                logger.debug(f"Cache set: {key} (TTL: {ttl}s)")
                return bool(result)
            except (RedisError, TypeError) as e:
                logger.warning(f"Redis set error for key {key}: {e}")
                return False
        
        return True
    
    def delete(self, key: str) -> bool:
        """Delete key from cache.
        
        Args:
            key: Cache key to delete
            
        Returns:
            bool: True if successful
        """
        # Remove from memory cache
        _memory_cache.pop(key, None)
        _memory_cache_timestamps.pop(key, None)
        
        # Remove from Redis
        if self._redis_client:
            try:
                result = self._redis_client.delete(key)
                logger.debug(f"Cache delete: {key}")
                return bool(result)
            except RedisError as e:
                logger.warning(f"Redis delete error for key {key}: {e}")
                return False
        
        return True
    
    def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching a pattern.
        
        Args:
            pattern: Pattern to match (supports wildcards)
            
        Returns:
            int: Number of keys deleted
        """
        deleted_count = 0
        
        # Clear matching keys from memory cache
        keys_to_delete = [k for k in _memory_cache.keys() if self._matches_pattern(k, pattern)]
        for key in keys_to_delete:
            _memory_cache.pop(key, None)
            _memory_cache_timestamps.pop(key, None)
            deleted_count += 1
        
        # Clear matching keys from Redis
        if self._redis_client:
            try:
                keys = self._redis_client.keys(pattern)
                if keys and isinstance(keys, list):
                    redis_deleted = self._redis_client.delete(*keys)
                    deleted_count += redis_deleted if isinstance(redis_deleted, int) else 0
                    logger.debug(f"Cache delete pattern: {pattern} ({redis_deleted} keys)")
            except RedisError as e:
                logger.warning(f"Redis delete pattern error for {pattern}: {e}")
        
        return deleted_count
    
    def _store_in_memory_cache(self, key: str, value: Any) -> None:
        """Store value in memory cache with size limit.
        
        Args:
            key: Cache key
            value: Value to store
        """
        # Check size limit
        if len(_memory_cache) >= self.config.memory_cache_max_size:
            # Remove oldest entries
            oldest_keys = sorted(
                _memory_cache_timestamps.keys(),
                key=lambda k: _memory_cache_timestamps[k]
            )[:10]  # Remove 10 oldest entries
            
            for old_key in oldest_keys:
                _memory_cache.pop(old_key, None)
                _memory_cache_timestamps.pop(old_key, None)
        
        _memory_cache[key] = value
        _memory_cache_timestamps[key] = datetime.now()
    
    def _matches_pattern(self, key: str, pattern: str) -> bool:
        """Check if key matches pattern (simple wildcard support).
        
        Args:
            key: Key to check
            pattern: Pattern with wildcards
            
        Returns:
            bool: True if key matches pattern
        """
        import fnmatch
        return fnmatch.fnmatch(key, pattern)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics.

        Returns:
            Dict with cache statistics
        """
        stats: Dict[str, Any] = {
            "memory_cache_size": len(_memory_cache),
            "memory_cache_max_size": self.config.memory_cache_max_size,
            "redis_connected": self._redis_client is not None,
        }
        
        if self._redis_client:
            try:
                info = self._redis_client.info()
                if isinstance(info, dict):
                    stats.update({
                        "redis_used_memory": info.get("used_memory_human", "N/A"),
                        "redis_connected_clients": info.get("connected_clients", 0),
                        "redis_total_commands_processed": info.get("total_commands_processed", 0),
                    })
            except RedisError as e:
                logger.warning(f"Error getting Redis stats: {e}")
                stats["redis_error"] = str(e)
        
        return stats


# Global cache manager instance
cache_manager = AdvancedCacheManager()


def cached(
    ttl: Optional[int] = None,
    key_prefix: str = "cached",
    invalidate_patterns: Optional[List[str]] = None
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for caching function results.
    
    Args:
        ttl: Time to live in seconds
        key_prefix: Prefix for cache keys
        invalidate_patterns: Patterns to invalidate when function is called
        
    Returns:
        Decorated function
    """
    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            # Generate cache key
            cache_key = cache_manager._generate_cache_key(
                f"{key_prefix}:{func.__name__}",
                *args,
                **kwargs
            )
            
            # Try to get from cache
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function
            result = func(*args, **kwargs)
            
            # Store in cache
            cache_manager.set(cache_key, result, ttl)
            
            # Invalidate patterns if specified
            if invalidate_patterns:
                for pattern in invalidate_patterns:
                    cache_manager.delete_pattern(pattern)
            
            return result
        
        return wrapper
    return decorator
